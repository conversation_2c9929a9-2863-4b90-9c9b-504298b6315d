import { apiService } from './api';

export class GeminiService {
  async generateFinancialAdvice(prompt: string): Promise<string> {
    try {
      const response = await apiService.sendChatMessage(prompt);
      return response.response;
    } catch (error) {
      console.error('Financial advice error:', error);
      throw new Error('Failed to generate financial advice. Please try again.');
    }
  }
}

export const geminiService = new GeminiService();
