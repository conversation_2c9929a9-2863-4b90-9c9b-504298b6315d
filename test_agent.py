import os
import sys
sys.path.append('backend')

# Load environment variables
from dotenv import load_dotenv
load_dotenv('backend/multi_tool_agent/.env')

# Test the agent directly
try:
    from backend.multi_tool_agent.agent import root_agent
    print("Agent imported successfully")
    
    # Test a simple message
    response = root_agent.run("Hello, can you help me with budgeting?")
    print(f"Agent response: {response}")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
