from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List
import os
from dotenv import load_dotenv
from pydantic import BaseModel

# Import from our modules
from database import (
    get_db, init_mock_data, SessionLocal,
    TransactionDB, BudgetDB, GoalDB,
    Transaction, TransactionCreate,
    Budget, BudgetCreate,
    Goal, GoalCreate
)

# Import ADK agent and runner
from multi_tool_agent.agent import root_agent
from google.adk.runner import Runner
from google.adk.sessions import InMemorySessionService

# Load environment variables
load_dotenv()
load_dotenv(os.path.join(os.path.dirname(__file__), 'multi_tool_agent', '.env'))

# Pydantic Models
class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str

class AIRequest(BaseModel):
    prompt: str

class CategoryRequest(BaseModel):
    description: str
    amount: float

# Initialize ADK runner
session_service = InMemorySessionService()
runner = Runner(root_agent, "financial_advisor_app", None, session_service)

# Initialize data on startup
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    db = SessionLocal()
    init_mock_data(db)
    db.close()
    yield

# FastAPI app
app = FastAPI(title="AI Finance Assistant API", version="1.0.0", lifespan=lifespan)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check
@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "message": "AI Finance Assistant API is running"}

# Main chat endpoint using ADK agent
@app.post("/api/chat", response_model=ChatResponse)
async def chat_with_agent(request: ChatRequest):
    try:
        print(f"Received chat request: {request.message}")

        # Create a session for this conversation
        user_id = "default_user"
        session_id = "default_session"

        # Get or create session
        try:
            session = session_service.getSession("financial_advisor_app", user_id, session_id).blockingGet()
        except:
            # Create new session if it doesn't exist
            session = session_service.createSession("financial_advisor_app", user_id, {}, session_id).blockingGet()

        # Create user message
        from google.genai.types import Content, Part
        user_message = Content(role="user", parts=[Part(text=request.message)])

        # Run the agent
        events = runner.run(session, user_message)

        # Extract the response from the last event
        response_text = ""
        for event in events:
            if hasattr(event, 'content') and event.content and hasattr(event.content, 'parts'):
                for part in event.content.parts:
                    if hasattr(part, 'text') and part.text:
                        response_text += part.text

        print(f"Agent response: {response_text}")
        return ChatResponse(response=response_text)
    except Exception as e:
        print(f"Chat error: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")

# Keep existing database endpoints for the app functionality
@app.get("/api/transactions", response_model=List[Transaction])
async def get_transactions(db: Session = Depends(get_db)):
    transactions = db.query(TransactionDB).order_by(TransactionDB.date.desc()).all()
    return transactions

@app.post("/api/transactions", response_model=Transaction)
async def create_transaction(transaction: TransactionCreate, db: Session = Depends(get_db)):
    db_transaction = TransactionDB(**transaction.dict())
    db.add(db_transaction)
    db.commit()
    db.refresh(db_transaction)
    return db_transaction

@app.delete("/api/transactions/{transaction_id}")
async def delete_transaction(transaction_id: int, db: Session = Depends(get_db)):
    transaction = db.query(TransactionDB).filter(TransactionDB.id == transaction_id).first()
    if not transaction:
        raise HTTPException(status_code=404, detail="Transaction not found")
    db.delete(transaction)
    db.commit()
    return {"message": "Transaction deleted successfully"}

# Budget endpoints
@app.get("/api/budgets", response_model=List[Budget])
async def get_budgets(db: Session = Depends(get_db)):
    budgets = db.query(BudgetDB).all()
    return budgets

@app.post("/api/budgets", response_model=Budget)
async def create_budget(budget: BudgetCreate, db: Session = Depends(get_db)):
    db_budget = BudgetDB(**budget.dict())
    db.add(db_budget)
    db.commit()
    db.refresh(db_budget)
    return db_budget

# Goal endpoints
@app.get("/api/goals", response_model=List[Goal])
async def get_goals(db: Session = Depends(get_db)):
    goals = db.query(GoalDB).all()
    return goals

@app.post("/api/goals", response_model=Goal)
async def create_goal(goal: GoalCreate, db: Session = Depends(get_db)):
    db_goal = GoalDB(**goal.dict())
    db.add(db_goal)
    db.commit()
    db.refresh(db_goal)
    return db_goal

@app.put("/api/goals/{goal_id}", response_model=Goal)
async def update_goal(goal_id: int, current_amount: float, db: Session = Depends(get_db)):
    goal = db.query(GoalDB).filter(GoalDB.id == goal_id).first()
    if not goal:
        raise HTTPException(status_code=404, detail="Goal not found")
    goal.current_amount = current_amount
    db.commit()
    db.refresh(goal)
    return goal

# AI endpoints using ADK agent
@app.post("/api/ai/advice")
async def ai_advice(request: AIRequest):
    try:
        # Use ADK agent to get financial advice
        response = root_agent.run(request.prompt)
        return {"advice": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate advice: {str(e)}")

@app.post("/api/ai/categorize")
async def ai_categorize(request: CategoryRequest):
    try:
        # Create a prompt for categorization
        categorize_prompt = f"""
        Please categorize this transaction: "{request.description}" with amount {abs(request.amount)}.
        Choose from: Food & Dining, Shopping, Transportation, Bills & Utilities, Entertainment, Healthcare, Travel, Education, Income, Other.
        Respond with only the category name.
        """
        
        response = root_agent.run(categorize_prompt)
        
        # Validate category
        valid_categories = [
            'Food & Dining', 'Shopping', 'Transportation', 'Bills & Utilities',
            'Entertainment', 'Healthcare', 'Travel', 'Education', 'Income', 'Other'
        ]
        
        category = response.strip()
        if category not in valid_categories:
            category = 'Other'
            
        return {"category": category}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to categorize: {str(e)}")

@app.post("/api/ai/budget-insights")
async def ai_budget_insights(db: Session = Depends(get_db)):
    try:
        transactions = db.query(TransactionDB).filter(TransactionDB.type == "expense").all()
        
        if not transactions:
            return {"insights": "No spending data available yet. Start adding transactions to get personalized budget insights."}
        
        total_spent = sum(abs(t.amount) for t in transactions)
        categories = {}
        for t in transactions:
            categories[t.category] = categories.get(t.category, 0) + abs(t.amount)
        
        top_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)[:3]
        
        insights_prompt = f"""
        Analyze my spending patterns and provide budget insights:
        
        Total spent this month: ${total_spent:.2f}
        Top spending categories: {', '.join([f'{cat}: ${amount:.2f}' for cat, amount in top_categories])}
        
        Provide 2-3 key insights about my spending patterns and actionable budget recommendations.
        Keep it concise and helpful.
        """
        
        response = root_agent.run(insights_prompt)
        return {"insights": response}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate insights: {str(e)}")

# Analytics endpoints
@app.get("/api/analytics/spending")
async def get_spending_analytics(db: Session = Depends(get_db)):
    transactions = db.query(TransactionDB).filter(TransactionDB.type == "expense").all()
    
    spending_by_category = {}
    total_expenses = 0
    
    for transaction in transactions:
        category = transaction.category
        amount = abs(transaction.amount)
        spending_by_category[category] = spending_by_category.get(category, 0) + amount
        total_expenses += amount
    
    return {
        "spending_by_category": spending_by_category,
        "total_expenses": total_expenses
    }

@app.get("/api/analytics/balance")
async def get_balance(db: Session = Depends(get_db)):
    transactions = db.query(TransactionDB).all()
    total_balance = sum(t.amount for t in transactions)
    
    income = sum(t.amount for t in transactions if t.type == "income")
    expenses = abs(sum(t.amount for t in transactions if t.type == "expense"))
    
    return {
        "total_balance": total_balance,
        "monthly_income": income,
        "monthly_expenses": expenses
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
