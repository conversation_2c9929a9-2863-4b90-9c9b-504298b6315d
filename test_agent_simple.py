import asyncio
import sys
sys.path.append('backend')

# Load environment variables
from dotenv import load_dotenv
load_dotenv('backend/multi_tool_agent/.env')

async def test_agent():
    try:
        from backend.multi_tool_agent.agent import root_agent
        print("Agent imported successfully")
        print(f"Agent type: {type(root_agent)}")
        
        # Test the run_async method
        session_context = {
            "user_id": "default_user",
            "session_id": "default_session",
            "app_name": "financial_advisor_app"
        }
        
        response_text = ""
        async for event in root_agent.run_async("Hello, can you help me with budgeting?"):
            print(f"Event: {event}")
            # Extract text from the event
            if hasattr(event, 'content') and event.content:
                if hasattr(event.content, 'parts'):
                    for part in event.content.parts:
                        if hasattr(part, 'text') and part.text:
                            response_text += part.text
                elif hasattr(event.content, 'text'):
                    response_text += event.content.text
            elif hasattr(event, 'text'):
                response_text += event.text

        print(f"Agent response: {response_text}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_agent())
