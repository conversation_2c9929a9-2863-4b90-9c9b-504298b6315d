const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:8000';

export interface Transaction {
  id: number;
  description: string;
  amount: number;
  category: string;
  date: string;
  type: 'income' | 'expense';
}

export interface Budget {
  id: number;
  category: string;
  limit: number;
  spent: number;
  period: 'weekly' | 'monthly';
}

export interface Goal {
  id: number;
  title: string;
  target_amount: number;
  current_amount: number;
  deadline: string;
  category: string;
}

export interface ChatMessage {
  message: string;
}

export interface ChatResponse {
  response: string;
}

class ApiService {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: string; message: string }> {
    return this.request('/api/health');
  }

  // Transactions
  async getTransactions(): Promise<Transaction[]> {
    return this.request('/api/transactions');
  }

  async addTransaction(transaction: Omit<Transaction, 'id'>): Promise<Transaction> {
    return this.request('/api/transactions', {
      method: 'POST',
      body: JSON.stringify({
        ...transaction,
        date: new Date(transaction.date).toISOString(),
      }),
    });
  }

  async deleteTransaction(id: number): Promise<void> {
    await this.request(`/api/transactions/${id}`, {
      method: 'DELETE',
    });
  }

  // Budgets
  async getBudgets(): Promise<Budget[]> {
    return this.request('/api/budgets');
  }

  async addBudget(budget: Omit<Budget, 'id' | 'spent'>): Promise<Budget> {
    return this.request('/api/budgets', {
      method: 'POST',
      body: JSON.stringify(budget),
    });
  }

  // Goals
  async getGoals(): Promise<Goal[]> {
    return this.request('/api/goals');
  }

  async addGoal(goal: Omit<Goal, 'id'>): Promise<Goal> {
    return this.request('/api/goals', {
      method: 'POST',
      body: JSON.stringify({
        ...goal,
        deadline: new Date(goal.deadline).toISOString(),
      }),
    });
  }

  async updateGoal(id: number, currentAmount: number): Promise<Goal> {
    return this.request(`/api/goals/${id}?current_amount=${currentAmount}`, {
      method: 'PUT',
    });
  }

  // AI Services
  async getFinancialAdvice(prompt: string): Promise<{ advice: string }> {
    return this.request('/api/ai/advice', {
      method: 'POST',
      body: JSON.stringify({ prompt }),
    });
  }

  async categorizeTransaction(description: string, amount: number): Promise<{ category: string }> {
    return this.request('/api/ai/categorize', {
      method: 'POST',
      body: JSON.stringify({ description, amount }),
    });
  }

  async getBudgetInsights(): Promise<{ insights: string }> {
    return this.request('/api/ai/budget-insights', {
      method: 'POST',
    });
  }

  // Analytics
  async getSpendingAnalytics(): Promise<{
    spending_by_category: Record<string, number>;
    total_expenses: number;
  }> {
    return this.request('/api/analytics/spending');
  }

  async getBalanceAnalytics(): Promise<{
    total_balance: number;
    monthly_income: number;
    monthly_expenses: number;
  }> {
    return this.request('/api/analytics/balance');
  }

  // Updated chat method for ADK agent
  async sendChatMessage(message: string): Promise<ChatResponse> {
    return this.request<ChatResponse>('/api/chat', {
      method: 'POST',
      body: JSON.stringify({ message }),
    });
  }
}

export const apiService = new ApiService();
