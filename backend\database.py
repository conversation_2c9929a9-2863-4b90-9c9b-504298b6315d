from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel
from typing import List
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./database.db")
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database Models
class TransactionDB(Base):
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    description = Column(String, index=True)
    amount = Column(Float)
    category = Column(String)
    date = Column(DateTime)
    type = Column(String)  # 'income' or 'expense'

class BudgetDB(Base):
    __tablename__ = "budgets"
    
    id = Column(Integer, primary_key=True, index=True)
    category = Column(String, index=True)
    limit = Column(Float)
    spent = Column(Float, default=0.0)
    period = Column(String)  # 'weekly' or 'monthly'

class GoalDB(Base):
    __tablename__ = "goals"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    target_amount = Column(Float)
    current_amount = Column(Float, default=0.0)
    deadline = Column(DateTime)
    category = Column(String)

# Pydantic Models
class TransactionCreate(BaseModel):
    description: str
    amount: float
    category: str
    date: datetime
    type: str

class Transaction(BaseModel):
    id: int
    description: str
    amount: float
    category: str
    date: datetime
    type: str
    
    class Config:
        from_attributes = True

class BudgetCreate(BaseModel):
    category: str
    limit: float
    period: str

class Budget(BaseModel):
    id: int
    category: str
    limit: float
    spent: float
    period: str
    
    class Config:
        from_attributes = True

class GoalCreate(BaseModel):
    title: str
    target_amount: float
    current_amount: float
    deadline: datetime
    category: str

class Goal(BaseModel):
    id: int
    title: str
    target_amount: float
    current_amount: float
    deadline: datetime
    category: str
    
    class Config:
        from_attributes = True

# Create tables
Base.metadata.create_all(bind=engine)

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Initialize mock data
def init_mock_data(db: Session):
    # Check if data already exists
    if db.query(TransactionDB).first():
        return
    
    # Mock transactions
    mock_transactions = [
        TransactionDB(
            description="Grocery shopping at Whole Foods",
            amount=-125.50,
            category="Food & Dining",
            date=datetime.now(),
            type="expense"
        ),
        TransactionDB(
            description="Salary deposit",
            amount=3500.00,
            category="Income",
            date=datetime.now(),
            type="income"
        ),
        TransactionDB(
            description="Netflix subscription",
            amount=-15.99,
            category="Entertainment",
            date=datetime.now(),
            type="expense"
        )
    ]
    
    # Mock budgets
    mock_budgets = [
        BudgetDB(category="Food & Dining", limit=400, spent=134.25, period="monthly"),
        BudgetDB(category="Transportation", limit=200, spent=45.20, period="monthly"),
        BudgetDB(category="Entertainment", limit=100, spent=15.99, period="monthly")
    ]
    
    # Mock goals
    mock_goals = [
        GoalDB(
            title="Emergency Fund",
            target_amount=10000,
            current_amount=2500,
            deadline=datetime(2025, 12, 31),
            category="Savings"
        ),
        GoalDB(
            title="Vacation to Japan",
            target_amount=5000,
            current_amount=1200,
            deadline=datetime(2025, 8, 15),
            category="Travel"
        )
    ]
    
    # Add to database
    for item in mock_transactions + mock_budgets + mock_goals:
        db.add(item)
    db.commit()