import os
import sys
import subprocess
import threading
import time
import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import httpx

# Create a FastAPI app that will act as a proxy
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
)

# Define the ADK server URL
ADK_SERVER_URL = "http://localhost:8001"  # ADK server will run on port 8001

# Create a proxy for all requests
@app.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])
async def proxy(request: Request, path: str):
    # Construct the target URL
    target_url = f"{ADK_SERVER_URL}/{path}"

    # Get the request body
    body = await request.body()

    # Forward the request to the ADK server
    async with httpx.AsyncClient() as client:
        response = await client.request(
            method=request.method,
            url=target_url,
            headers={key: value for key, value in request.headers.items() if key.lower() != "host"},
            content=body,
        )

    # Return the response from the ADK server
    return Response(
        content=response.content,
        status_code=response.status_code,
        headers=dict(response.headers),
    )

# Function to start the ADK server
def start_adk_server():
    # Start the ADK server on port 8001
    subprocess.Popen(
        ["python", "-m", "google.adk.cli", "api_server", "--port", "8001"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )
    print("ADK server started on port 8001")

# Function to run the proxy server
def run_proxy_server():
    # Start the proxy server on port 8000
    uvicorn.run(app, host="0.0.0.0", port=8000)
    print("Proxy server started on port 8000")

if __name__ == "__main__":
    # Make sure we're in the correct directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Run the server
    run_adk_server_with_cors()
