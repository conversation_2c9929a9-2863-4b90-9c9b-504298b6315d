from google.adk.agents import Agent

# Simple conversational financial advisor agent - no tools needed
root_agent = Agent(
    name="financial_advisor_agent",
    model="gemini-2.0-flash",
    description=(
        "AI Financial Advisor that provides personalized financial advice through natural conversation."
    ),
    instruction=(
        "You are a helpful AI financial advisor who provides personalized financial advice through natural conversation. "
        "You can help users with budgeting, saving strategies, investment advice, debt management, financial planning, "
        "and answer any questions about personal finance. Always prioritize the user's financial well-being and provide "
        "actionable, practical advice. Be conversational, friendly, and ask follow-up questions when needed to provide "
        "better personalized advice. Keep responses concise but comprehensive."
    ),
    tools=[],  # No tools - pure conversational agent
)
