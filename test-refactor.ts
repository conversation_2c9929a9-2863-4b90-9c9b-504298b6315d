// Quick test to verify the refactoring worked correctly
import { apiService, Transaction, Budget, Goal } from './services/api';

// Test that interfaces are properly exported and types are correct
const testTransaction: Transaction = {
  id: 1,
  description: "Test transaction",
  amount: 100,
  category: "Food",
  date: "2024-01-01",
  type: "expense"
};

const testBudget: Budget = {
  id: 1,
  category: "Food",
  limit: 500,
  spent: 100,
  period: "monthly"
};

const testGoal: Goal = {
  id: 1,
  title: "Emergency Fund",
  target_amount: 10000,
  current_amount: 2500,
  deadline: "2024-12-31",
  category: "Savings"
};

console.log("Refactoring test passed - all types are correct!");