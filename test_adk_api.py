import requests
import json

def test_adk_api():
    base_url = "http://localhost:8000"
    
    # Step 1: Create a session
    app_name = "multi_tool_agent"
    user_id = "test_user"
    session_id = "test_session"
    
    session_url = f"{base_url}/apps/{app_name}/users/{user_id}/sessions/{session_id}"
    
    print("Creating session...")
    try:
        session_response = requests.post(
            session_url,
            headers={"Content-Type": "application/json"},
            json={"state": {}}
        )
        print(f"Session creation status: {session_response.status_code}")
        if session_response.status_code not in [200, 201]:
            print(f"Session response: {session_response.text}")
    except Exception as e:
        print(f"Session creation error: {e}")
    
    # Step 2: Send a message
    print("\nSending message...")
    run_url = f"{base_url}/run"
    
    message_data = {
        "appName": app_name,
        "userId": user_id,
        "sessionId": session_id,
        "newMessage": {
            "role": "user",
            "parts": [{
                "text": "Hello, can you help me with budgeting?"
            }]
        }
    }
    
    try:
        response = requests.post(
            run_url,
            headers={"Content-Type": "application/json"},
            json=message_data
        )
        print(f"Message status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            events = response.json()
            print(f"\nReceived {len(events)} events")
            
            # Extract response text
            response_text = ""
            for event in events:
                if event.get('content') and event['content'].get('parts'):
                    for part in event['content']['parts']:
                        if part.get('text'):
                            response_text += part['text']
            
            print(f"Agent response: {response_text}")
        
    except Exception as e:
        print(f"Message error: {e}")

if __name__ == "__main__":
    test_adk_api()
