import requests
import json

def test_chat_api():
    url = "http://localhost:8000/api/chat"
    headers = {"Content-Type": "application/json"}
    data = {"message": "Hello, can you help me with budgeting?"}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_chat_api()
